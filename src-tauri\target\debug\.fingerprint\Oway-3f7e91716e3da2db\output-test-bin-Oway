{"$message_type": "diagnostic", "message": "use of deprecated associated function `chrono::NaiveDateTime::from_timestamp_opt`: use `DateTime::from_timestamp` instead", "code": {"code": "deprecated", "explanation": null}, "level": "warning", "spans": [{"file_name": "src\\emails_functions\\parsing_email.rs", "byte_start": 2487, "byte_end": 2505, "line_start": 75, "line_end": 75, "column_start": 35, "column_end": 53, "is_primary": true, "text": [{"text": "    let mut date = NaiveDateTime::from_timestamp_opt(0, 0).unwrap();", "highlight_start": 35, "highlight_end": 53}], "label": null, "suggested_replacement": null, "suggestion_applicability": null, "expansion": null}], "children": [{"message": "`#[warn(deprecated)]` on by default", "code": null, "level": "note", "spans": [], "children": [], "rendered": null}], "rendered": "\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated associated function `chrono::NaiveDateTime::from_timestamp_opt`: use `DateTime::from_timestamp` instead\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\emails_functions\\parsing_email.rs:75:35\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut date = NaiveDateTime::from_timestamp_opt(0, 0).unwrap();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}