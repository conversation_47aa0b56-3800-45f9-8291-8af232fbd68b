import { invoke } from "@tauri-apps/api/core";
import { CalUser } from "../models/user";
import { EmailContext, PhoneCallContext, User } from "../types";

export interface UserData {
  user: User; // Define the User interface separately
  calInfo?: CalUser | null;
  refreshToken?: string | null; // oauth2::RefreshToken is usually a string
  expireIn?: number | null; // Duration → number (in ms or seconds, depending on your API)
  accessToken?: string | null; // oauth2::AccessToken is usually a string
  issuedAt?: string | null; // DateTime<Utc> → ISO string
  emailContext?: EmailContext | null; // Define EmailContext separately
  phoneContext?: PhoneCallContext | null; // Define PhoneCallContext separately
  watchExpiration?: number | null; // i64 in ms → number
  historyId?: string | null;
}



async function openMainWindow() {
  try {
    await invoke("start_main_window");
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}

async function openAuthWindow() {
  try {
    await invoke("start_auth_window");
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}


async function getLocalUserInfo() {
  try {
    const res = await invoke<any>("get_local_user_info");
    return res;
  } catch (error) {
    console.error(error);
    return false;
  }
}

async function updateCalInfo(calInfo: CalUser): Promise<UserData | false> {
  try {
    const res = await invoke<UserData>("update_cal_info", { info: calInfo });
    return res;
  } catch (error) {
    console.error(error);
    return false;
  }
}

async function logout() {
  invoke("js2rs", {
    message: "logout",
  }).then(async () => {
    try {
      await auth.openAuthWindow();
    } catch (error) {
      console.error(error);
    }
  });
}


const auth = {
  openMainWindow,
  openAuthWindow,
  getLocalUserInfo,
  updateCalInfo,
  logout
};

export default auth;
